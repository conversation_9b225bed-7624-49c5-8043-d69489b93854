# Restauración Dinámica de DB2 con Docker Compose

Este proyecto permite levantar y restaurar una base de datos DB2 de forma dinámica utilizando Docker y un fichero de backup.

## 1. Configuración

Antes de empezar, configura las variables de entorno en el fichero `.env`. Asegúrate de que los siguientes valores son correctos:

- `CONTAINER_NAME`: El nombre que tendrá el contenedor de Docker.
- `DB_NAME`: El nombre de la base de datos que quieres restaurar (ej. `GNPDB296`).
- `LOCAL_BACKUP_PATH`: La ruta en tu máquina local al fichero de backup (ej. `./backup/MI_BACKUP.001`).
- `DB_TIMESTAMP`: El timestamp que se encuentra en el nombre del fichero de backup.

## 2. Comandos

> **Nota:** Ejecuta todos los comandos desde el directorio raíz del proyecto (donde está el `docker-compose.yml`). [cite_start]En Windows, usa **WSL** o **Git Bash**. [cite: 1, 2]

> **Importante:** Carga las variables del `.env` en tu terminal para usarlas en los comandos:
>
> ```bash
> export $(grep -v '^#' .env | xargs)
> ```

1.  **Limpiar (si es necesario)**
    _Elimina el contenedor y el volumen para una instalación limpia._

    ```bash
    docker-compose down -v
    docker rm --force ${CONTAINER_NAME}
    ```

2.  **Iniciar Contenedor**
    _Inicia el contenedor de DB2 en segundo plano._

    ```bash
    docker-compose up -d
    ```

3.  **Copiar Backup**
    _Copia el fichero de backup (definido en `.env`) al interior del contenedor._

    ```bash
    docker cp ${LOCAL_BACKUP_PATH} ${CONTAINER_NAME}:/tmp/
    docker cp .env gnp-db2:/scripts/.env
    ```

4.  **Ejecutar Restauración**
    _Entra al contenedor y ejecuta el script de restauración. El script leerá las variables de entorno para saber qué y cómo restaurar._

    ```bash
    docker exec -u root gnp-db2 chmod +x /scripts/restore.sh
    docker exec -it -u db2inst1 gnp-db2 bash
    ```

    ```bash
    /scripts/restore.sh
    ```

5.  **Monitorear Logs (Opcional)**
    _Puedes ver los logs en tiempo real para seguir el proceso._
    ```bash
    docker-compose logs -f
    ```

## 3. Datos de Conexión

Una vez finalizada la restauración, puedes conectarte a la base de datos con los siguientes datos (basados en tu fichero `.env`):

- **Host**: `localhost:${DB_HOST_PORT}`
- **Base de Datos**: `${DB_NAME}`
- **Usuario**: `${DB2_INSTANCE}`
- **Contraseña**: `${DB2_PASSWORD}`
