#!/bin/bash

# Configurar entorno DB2
source /home/<USER>/sqllib/db2profile

# Cargar variables del .env si existe
if [ -f "/scripts/.env" ]; then
    export $(grep -v '^#' /scripts/.env | xargs)
fi

# Usar variables del .env o valores por defecto
BACKUP_FILENAME=$(basename ${LOCAL_BACKUP_PATH})
BACKUP_PATH="/tmp/${BACKUP_FILENAME}"
DB_NAME=${DB_NAME:-"GNPDB296"}
TIMESTAMP=${DB_TIMESTAMP}

echo "=========================================="
echo "INICIANDO PROCESO DE RESTAURACIÓN"
echo "-> Fichero: ${BACKUP_PATH}"
echo "-> Base de datos: ${DB_NAME}"
echo "-> Timestamp: ${TIMESTAMP}"
echo "------------------------------------------"

# Verificar que el archivo existe
if [ ! -f "$BACKUP_PATH" ]; then
    echo "ERROR: No se encontró el fichero '$BACKUP_PATH'."
    echo "Asegúrate de haberlo copiado primero con 'docker cp'."
    exit 1
fi

# Iniciar la instancia DB2
echo "   -> Iniciando instancia DB2..."
db2start

echo "   -> Esperando a que DB2 esté listo..."
while ! db2 get instance | grep -q "db2inst1"; do
    sleep 5
    echo "   DB2 aún no está listo, reintentando..."
done
echo "   DB2 está listo."

echo "   -> Limpiando base de datos '${DB_NAME}' si existe..."
db2 force applications all >/dev/null 2>&1
db2 deactivate database ${DB_NAME} >/dev/null 2>&1
db2 drop database ${DB_NAME} >/dev/null 2>&1
sleep 5

echo "   -> Iniciando proceso de restore..."

# Ejecutar todo el proceso de restore en una sola sesión de DB2
db2 -v << 'EOF'
RESTORE DATABASE GNPDB296 FROM '/tmp' TAKEN AT 20250703055039 INTO GNPDB296 NEWLOGPATH '/database/logs/GNPDB296' REDIRECT WITHOUT ROLLING FORWARD;
RESTORE DATABASE GNPDB296 CONTINUE;
ROLLFORWARD DATABASE GNPDB296 TO END OF LOGS AND COMPLETE;
CONNECT TO GNPDB296;
SET STOGROUP PATHS FOR IBMSTOGROUP ON '/database/data';
SELECT COUNT(*) AS TOTAL_TABLES FROM SYSCAT.TABLES WHERE TYPE='T';
CONNECT RESET;
EOF

if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "PROCESO DE RESTAURACIÓN FINALIZADO CON ÉXITO."
    echo "=========================================="
else
    echo "ERROR: Falló el proceso de restore."
    exit 1
fi