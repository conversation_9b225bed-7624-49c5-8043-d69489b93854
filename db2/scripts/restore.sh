#!/bin/bash

# Configurar entorno DB2
source /home/<USER>/sqllib/db2profile

# Cargar variables del .env si existe
if [ -f "/scripts/.env" ]; then
    export $(grep -v '^#' /scripts/.env | xargs)
fi

# Usar variables del .env o valores por defecto
BACKUP_FILENAME=$(basename ${LOCAL_BACKUP_PATH})
BACKUP_PATH="/tmp/${BACKUP_FILENAME}"
DB_NAME=${DB_NAME:-"GNPDB296"}
TIMESTAMP=${DB_TIMESTAMP}

echo "=========================================="
echo "INICIANDO PROCESO DE RESTAURACIÓN"
echo "-> Fichero: ${BACKUP_PATH}"
echo "-> Base de datos: ${DB_NAME}"
echo "-> Timestamp: ${TIMESTAMP}"
echo "------------------------------------------"

# Verificar que el archivo existe
if [ ! -f "$BACKUP_PATH" ]; then
    echo "ERROR: No se encontró el fichero '$BACKUP_PATH'."
    echo "Asegúrate de haberlo copiado primero con 'docker cp'."
    exit 1
fi

# Iniciar la instancia DB2
echo "   -> Iniciando instancia DB2..."
db2start

echo "   -> Esperando a que DB2 esté listo..."
while ! db2 get instance | grep -q "db2inst1"; do
    sleep 5
    echo "   DB2 aún no está listo, reintentando..."
done
echo "   DB2 está listo."

echo "   -> Limpiando base de datos '${DB_NAME}' si existe..."
db2 force applications all >/dev/null 2>&1
db2 deactivate database ${DB_NAME} >/dev/null 2>&1
db2 drop database ${DB_NAME} >/dev/null 2>&1
sleep 5

echo "   -> Iniciando proceso de restore..."
RESTORE_OUTPUT=$(db2 "RESTORE DATABASE ${DB_NAME} FROM '/tmp' TAKEN AT ${TIMESTAMP} INTO ${DB_NAME} NEWLOGPATH '/database/logs/${DB_NAME}' REDIRECT WITHOUT ROLLING FORWARD" 2>&1)
echo "$RESTORE_OUTPUT"

# Verificar si el primer paso del restore fue exitoso
if echo "$RESTORE_OUTPUT" | grep -q "DB20000I.*completed successfully"; then
    echo "   -> Continuando restore..."
    CONTINUE_OUTPUT=$(db2 "RESTORE DATABASE ${DB_NAME} CONTINUE" 2>&1)
    echo "$CONTINUE_OUTPUT"

    # Verificar si el continue fue exitoso
    if echo "$CONTINUE_OUTPUT" | grep -q "DB20000I.*completed successfully"; then
        echo "   -> Finalizando (Rollforward)..."
        db2 "ROLLFORWARD DATABASE ${DB_NAME} TO END OF LOGS AND COMPLETE"

        echo "   -> Configurando paths de almacenamiento..."
        db2 "connect to ${DB_NAME}"
        db2 "SET STOGROUP PATHS FOR IBMSTOGROUP ON '/database/data'"

        echo "   -> Verificando tablas..."
        db2 "select count(*) as total_tables from syscat.tables where type='T'"
        db2 "connect reset"

        echo "=========================================="
        echo "PROCESO DE RESTAURACIÓN FINALIZADO CON ÉXITO."
        echo "=========================================="
    else
        echo "ERROR: Falló el comando RESTORE CONTINUE."
        exit 1
    fi
else
    echo "ERROR: Falló el proceso de restore inicial."
    exit 1
fi