#!/bin/bash

# Script de restauración automática de DB2
# Este script se ejecuta DENTRO del contenedor DB2

echo "=========================================="
echo "INICIANDO PROCESO DE RESTAURACIÓN DE DB2"
echo "=========================================="

# Cargar variables del .env si existe
if [ -f "/scripts/.env" ]; then
    source /scripts/.env
    echo "Variables cargadas desde /scripts/.env"
else
    echo "ADVERTENCIA: No se encontró /scripts/.env, usando valores por defecto"
fi

# Variables (con valores por defecto si no están en .env)
BACKUP_FILENAME=$(basename ${LOCAL_BACKUP_PATH:-"GNPDB296.0.db2inst1.DBPART000.20250703055039.001"})
BACKUP_FILE="/tmp/${BACKUP_FILENAME}"
DB_NAME=${DB_NAME:-"GNPDB296"}
TIMESTAMP=${DB_TIMESTAMP:-"20250703055039"}

echo "Configuración:"
echo "  - Base de datos: $DB_NAME"
echo "  - Archivo backup: $BACKUP_FILE"
echo "  - Timestamp: $TIMESTAMP"

# Función para verificar si DB2 está listo
wait_for_db2() {
    echo "Esperando a que DB2 esté completamente iniciado..."
    local max_attempts=60
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if su - db2inst1 -c "db2 get instance" 2>/dev/null | grep -q "db2inst1"; then
            echo "DB2 está listo!"
            return 0
        fi
        echo "Intento $attempt/$max_attempts - DB2 aún no está listo..."
        sleep 5
        ((attempt++))
    done

    echo "ERROR: DB2 no se inició después de $max_attempts intentos"
    return 1
}

# Esperar a que DB2 esté listo
wait_for_db2

# Verificar que el archivo de backup existe
if [ ! -f "$BACKUP_FILE" ]; then
    echo "ERROR: No se encontró el archivo de backup en $BACKUP_FILE"
    exit 1
fi

echo "Archivo de backup encontrado: $BACKUP_FILE"
echo "Tamaño del backup: $(ls -lh $BACKUP_FILE | awk '{print $5}')"

# Limpiar base de datos existente
echo "Limpiando base de datos existente si existe..."
su - db2inst1 -c "db2 force applications all" 2>/dev/null
sleep 5
su - db2inst1 -c "db2 drop database $DB_NAME" 2>/dev/null
sleep 10

# Verificar integridad del backup
echo "Verificando integridad del backup..."
su - db2inst1 -c "db2ckbkp $BACKUP_FILE"

# Ejecutar el restore como db2inst1
echo "Iniciando proceso de restore..."
su - db2inst1 <<EOF
echo "--- 1/5: Creando estructura de directorios ---"
mkdir -p /database/data /database/logs/$DB_NAME

echo "--- 2/5: Iniciando Restore (Redirect) ---"
db2 RESTORE DATABASE $DB_NAME FROM '/tmp' TAKEN AT $TIMESTAMP INTO $DB_NAME NEWLOGPATH '/database/logs/$DB_NAME' REDIRECT WITHOUT ROLLING FORWARD WITHOUT PROMPTING

echo "--- 3/5: Redirigiendo Storage Group a la ruta persistente ---"
db2 SET STOGROUP PATHS FOR IBMSTOGROUP ON '/database/data'

echo "--- 4/5: Continuando Restore (moviendo datos) ---"
echo "   Este proceso puede tardar bastante tiempo dependiendo del tamaño del backup..."
echo "   Tamaño del backup: \$(ls -lh $BACKUP_FILE | awk '{print \$5}')"

# Iniciar restore y monitorear
db2 RESTORE DATABASE $DB_NAME CONTINUE &
RESTORE_PID=\$!

# Monitorear progreso
echo "   Monitoreando progreso del restore (actualización cada minuto)..."
START_TIME=\$(date +%s)

while kill -0 \$RESTORE_PID 2>/dev/null; do
    CURRENT_TIME=\$(date +%s)
    ELAPSED_MIN=\$(((CURRENT_TIME - START_TIME) / 60))

    # Get progress from 'db2 list utilities show detail'
    PROGRESS_INFO=\$(db2 list utilities show detail 2>/dev/null)
    WORK_COMPLETED=\$(echo "\$PROGRESS_INFO" | grep -A 20 "RESTORE" | grep "Completed Work" | sed 's/.*: //')

    if [ -n "\$WORK_COMPLETED" ]; then
        echo "   [\$(date '+%H:%M:%S')] Restore en progreso: \${WORK_COMPLETED} - Tiempo: \${ELAPSED_MIN} min"
    else
        echo "   [\$(date '+%H:%M:%S')] Restore en progreso... (Tiempo transcurrido: \${ELAPSED_MIN} min)"
    fi

    sleep 60
done

wait \$RESTORE_PID

echo "--- 5/5: Finalizando y poniendo la BD Online (Rollforward) ---"
db2 ROLLFORWARD DATABASE $DB_NAME TO END OF LOGS AND COMPLETE

echo "=========================================="
echo "RESTORE COMPLETADO!"
echo "=========================================="

# Verificar el resultado
echo "Verificando tablas..."
sleep 10
db2 connect to $DB_NAME
if [ \$? -eq 0 ]; then
    db2 "select count(*) as total_tables from syscat.tables where type='T'"
    db2 connect reset
    echo "Verificación completada exitosamente"
else
    echo "ADVERTENCIA: No se pudo conectar para verificar tablas, pero el restore puede haber sido exitoso"
fi
EOF